import 'package:flutter/material.dart';
import 'package:scaled_size/scaled_size.dart';
import 'package:selfeng/features/games/domain/models/game_word.dart';
import 'package:selfeng/shared/globals.dart';
// Assuming SpeakingStage enum is available, possibly from main_lesson.dart or another import
// For example: import 'package:selfeng/features/main_lesson/domain/models/speaking_stage.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class LoadingScreen extends StatelessWidget {
  final GameType gameType;
  const LoadingScreen({super.key, this.gameType = GameType.memoryFlash});

  @override
  Widget build(BuildContext context) {
    String imagePath;
    switch (gameType) {
      case GameType.wordScramble:
        imagePath = '$assetImageGames/word_scramble/SS1.4 - WS.png';
        break;
      case GameType.memoryFlash:
        imagePath = '$assetImageGames/memory_flash/SS1.4 - MF.png';
        break;
      default:
        imagePath = '$assetImageGames/memory_flash/SS1.4 - MF.png';
    }
    return ScaledSize(
      builder: () {
        return Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFFD84315), // Deep orange
                Color(0xFFFFA000), // Amber
              ],
            ),
            image: DecorationImage(
              image: AssetImage(imagePath),
              fit: BoxFit.fill,
            ),
          ),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: EdgeInsets.only(bottom: 10.vh),
              padding: EdgeInsets.symmetric(vertical: 2.vh, horizontal: 9.vw),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.br),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    spreadRadius: 6,
                    blurRadius: 30,
                    offset: Offset(0, 8), // changes position of shadow
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    context.loc.please_wait,
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium!.copyWith(color: Color(0xff93000F)),
                    // textScaler: TextScaler.linear(0.8),
                  ),
                  SizedBox(width: 2.vw),
                  CircularProgressIndicator(
                    color: Color(0xffB4A9A7),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xffFB0006),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
