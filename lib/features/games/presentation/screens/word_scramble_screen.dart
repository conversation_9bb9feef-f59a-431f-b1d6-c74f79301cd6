import 'dart:ui';

import 'package:audioplayers/audioplayers.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/games/domain/models/game_word.dart';
import 'package:selfeng/features/games/presentation/providers/game_completion_controller.dart';
import 'package:selfeng/features/games/presentation/providers/state/word_scramble_state.dart';
import 'package:selfeng/features/games/presentation/providers/word_scramble_controller.dart';
import 'package:selfeng/features/games/presentation/widgets/loading_screen.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/timer_controller.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class WordScrambleScreen extends ConsumerStatefulWidget {
  const WordScrambleScreen({super.key, required this.topic});
  final String topic;

  @override
  ConsumerState<WordScrambleScreen> createState() => _WordScrambleScreenState();
}

class _WordScrambleScreenState extends ConsumerState<WordScrambleScreen>
    with TickerProviderStateMixin {
  late AsyncValue<WordScrambleState> viewState;
  late WordScrambleController viewModel;
  late AsyncValue viewTimerState;
  late TimerController viewTimerModel;

  // Controllers and keys
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  late final AudioPlayer _player = AudioPlayer();
  bool _isAudioPlayed = false;

  @override
  Widget build(BuildContext context) {
    _initializeProviders();

    return switch (viewState) {
      AsyncData() =>
        _shouldShowMainContent()
            ? _buildBody()
            : LoadingScreen(gameType: GameType.wordScramble),
      AsyncError() => _buildBody(),
      AsyncLoading() => LoadingScreen(gameType: GameType.wordScramble),
      _ => const Text('loading'),
    };
  }

  bool _shouldShowMainContent() {
    return viewState.value != null && viewState.value!.topic != null;
  }

  void _initializeProviders() {
    final prov = wordScrambleControllerProvider(topic: widget.topic);
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);
    final provTimer = timerControllerProvider;
    viewTimerState = ref.watch(provTimer);
    viewTimerModel = ref.watch(provTimer.notifier);

    ref.listen(prov.select((value) => value), _handleGameStateChange);
    ref.listen(provTimer.select((value) => value), _handleTimerStateChange);
  }

  void _handleGameStateChange(
    AsyncValue<WordScrambleState>? previous,
    AsyncValue<WordScrambleState> next,
  ) {
    next.maybeWhen(
      error: (error, track) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(error.toString())));
      },
      orElse: () {},
    );
  }

  void _handleTimerStateChange(AsyncValue? previous, AsyncValue next) {
    if (next.value?.totalSecond != 0) return;

    viewTimerModel.stopTimer();
    final currentGameStage = viewState.value?.gameStage;
    if (currentGameStage == null) return;

    _handleGameStageTimeout(currentGameStage);
  }

  void _handleGameStageTimeout(GameStage gameStage) {
    switch (gameStage) {
      case GameStage.topic:
      case GameStage.result:
      case GameStage.finished:
        return;
      case GameStage.loading:
        viewModel.loadGameContent();
        return;
      case GameStage.game:
        viewModel.gameOver();
        return;
      case GameStage.gameOver:
        viewModel.submitAnswers();
        return;
    }
  }

  Widget _buildBody() {
    return Scaffold(key: _scaffoldKey, body: _buildBackgroundContainer());
  }

  Widget _buildBackgroundContainer() {
    return Container(
      decoration: _buildBackgroundDecoration(),
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: viewState.value!.gameStage == GameStage.game ? 10 : 0,
          sigmaY: viewState.value!.gameStage == GameStage.game ? 10 : 0,
        ),
        child: Container(
          color: Colors.grey.withValues(alpha: .1),
          alignment: Alignment.center,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            child: Stack(children: [_buildContent(), _buildTopBar()]),
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return BoxDecoration(
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFD84315), Color(0xFFFFA000)],
      ),
      image: DecorationImage(
        image: CachedNetworkImageProvider(viewState.value!.topic?.image ?? ''),
        fit: BoxFit.fill,
      ),
    );
  }

  Widget _buildTopBar() {
    return SafeArea(
      child: Row(
        mainAxisAlignment: viewState.value!.gameStage != GameStage.finished
            ? MainAxisAlignment.spaceBetween
            : MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (viewState.value!.gameStage != GameStage.finished)
            _buildBackButton(),
          if (viewState.value!.gameStage == GameStage.result)
            _buildNextButton(),
          if (viewState.value!.gameStage == GameStage.game)
            _buildTimerSection(),
          if (viewState.value!.gameStage == GameStage.finished)
            _buildCloseButton(),
        ],
      ),
    );
  }

  Widget _buildBackButton() {
    return _buildImageButton(
      imagePath: '$assetImageGames/memory_flash/Navigate back Game.png',
      onTap: () {
        viewModel.exitGame();
        context.pop();
        customNav(
          context,
          RouterName.topicGame,
          params: {"game": RouterName.wordScramble},
        );
      },
    );
  }

  Widget _buildNextButton() {
    return _buildImageButton(
      imagePath: '$assetImageGames/memory_flash/Next page Game.png',
      onTap: () => viewModel.finishGame(),
    );
  }

  Widget _buildTimerSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          _buildImageButton(
            imagePath: _getTimerIconPath(),
            onTap: () => _showInstructionDialog(),
            size: 40,
          ),
          const SizedBox(width: 4),
          _buildTimerText(),
        ],
      ),
    );
  }

  String _getTimerIconPath() {
    return '$assetImageGames/memory_flash/Timer-Game4.png';
  }

  Widget _buildTimerText() {
    final minutes = viewTimerModel.getMinute().toString().padLeft(2, '0');
    final seconds = viewTimerModel.getSecond().toString().padLeft(2, '0');

    return Text(
      '$minutes:$seconds',
      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
        color: Colors.white,
        fontWeight: FontWeight.w800,
      ),
    );
  }

  Widget _buildCloseButton() {
    return _buildImageButton(
      imagePath: '$assetImageGames/memory_flash/Close page scores Game.png',
      onTap: () {
        viewModel.exitGame();
        context.pop();
      },
    );
  }

  Widget _buildImageButton({
    required String imagePath,
    required VoidCallback onTap,
    double size = 30,
  }) {
    return InkWell(
      onTap: onTap,
      child: Image.asset(imagePath, height: size, width: size),
    );
  }

  void _showInstructionDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => _buildInstruction(),
    );
  }

  Widget _buildInstruction() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 80),
      padding: const EdgeInsets.symmetric(vertical: 46, horizontal: 42),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          begin: Alignment.centerRight,
          end: Alignment.centerLeft,
          colors: [Color(0xff8B000A), Color(0xffF33928), Color(0xffFF8D46)],
        ),
      ),
      child: Material(
        type: MaterialType.transparency,
        child: Column(
          children: [_buildInstructionHeader(), _buildInstructionContent()],
        ),
      ),
    );
  }

  Widget _buildInstructionHeader() {
    return Align(
      alignment: Alignment.topRight,
      child: InkWell(
        onTap: () => context.pop(),
        child: Image.asset(
          '$assetImageGames/memory_flash/Close page scores Game.png',
          height: 40,
          width: 40,
        ),
      ),
    );
  }

  Widget _buildInstructionContent() {
    return Column(
      children: [
        Text(
          context.loc.gameplay,
          style: Theme.of(
            context,
          ).textTheme.headlineLarge!.copyWith(color: Colors.white),
          textScaler: const TextScaler.linear(0.8),
        ),
        const SizedBox(height: 16),
        Text(
          context.loc.how_to_play_memory_flash,
          style: Theme.of(
            context,
          ).textTheme.headlineSmall!.copyWith(color: Colors.white),
          textScaler: const TextScaler.linear(0.8),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (viewState.value == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final currentGameStage = viewState.value?.gameStage;
    if (currentGameStage == null) {
      return LoadingScreen(gameType: GameType.wordScramble);
    }

    return switch (currentGameStage) {
      GameStage.topic => _buildLoadingScreenStage(),
      GameStage.loading => _buildLoadingScreenStage(),
      GameStage.game => _buildGameStage(),
      GameStage.result => _buildResultStage(),
      GameStage.finished => _buildFinishedStage(),
      GameStage.gameOver => _buildGameOver(),
    };
  }

  Widget _buildLoadingScreenStage() {
    final timerValue = viewTimerState.value?.totalSecond;
    final image = _getLoadingStageImage(timerValue);
    final height = _getLoadingStageHeight(timerValue);

    print('timerValue: $timerValue');

    if (timerValue == 2) {
      _playAudio('sounds/memory_flash/Ready Go.MP3');
    }

    return Center(child: _buildLoadingImage(image, height));
  }

  String _getLoadingStageImage(int? timerValue) {
    if (timerValue == null) return '$assetImageGames/memory_flash/Ready.png';

    return switch (timerValue) {
      0 || 1 => '$assetImageGames/memory_flash/Go!.png',
      3 => _getTopicIconSafely(),
      _ => '$assetImageGames/memory_flash/Ready.png',
    };
  }

  String _getTopicIconSafely() {
    try {
      final topic = viewState.valueOrNull?.topic;
      return topic?.icon ?? '$assetImageGames/memory_flash/Ready.png';
    } catch (e) {
      return '$assetImageGames/memory_flash/Ready.png';
    }
  }

  bool _isNetworkImage(String imagePath) {
    return imagePath.startsWith('http://') || imagePath.startsWith('https://');
  }

  Widget _buildLoadingImage(String imagePath, double height) {
    try {
      if (_isNetworkImage(imagePath)) {
        // Handle network images
        return Image.network(
          imagePath,
          height: height,
          fit: BoxFit.fitHeight,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return SizedBox(
              height: height,
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                      : null,
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            // Fallback to default local asset
            return Image.asset(
              '$assetImageGames/memory_flash/Ready.png',
              height: height,
              fit: BoxFit.fitHeight,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: height,
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                  ),
                );
              },
            );
          },
        );
      } else {
        // Handle local assets
        return Image.asset(
          imagePath,
          height: height,
          fit: BoxFit.fitHeight,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              height: height,
              color: Colors.grey[300],
              child: const Icon(Icons.image_not_supported, color: Colors.grey),
            );
          },
        );
      }
    } catch (e) {
      // Return a safe fallback widget
      return Container(
        height: height,
        color: Colors.grey[300],
        child: const Icon(Icons.error, color: Colors.red),
      );
    }
  }

  double _getLoadingStageHeight(int? timerValue) {
    return (timerValue != null && timerValue > 2) ? 200 : 94;
  }

  Widget _buildGameStage() {
    return SafeArea(child: Center(child: _buildGameArea()));
  }

  Widget _buildGameArea() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Move the letters into the right order.',
              style: Theme.of(
                context,
              ).textTheme.headlineLarge!.copyWith(color: Colors.white),
            ),
            SizedBox(height: 20),
            // Scrambled letters area (options area)
            _buildScrambledLetters(),
            SizedBox(height: 20),

            // Selected letters area (answer area)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.3),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: (viewState.value!.currentGame!.isCompleted)
                  ? _buildSelectedLetters()
                  : Stack(
                      children: [_buildBoxLetters(), _buildSelectedLetters()],
                    ),
            ),
            // Show result when word is completed
            if (viewState.value!.currentGame!.isCompleted)
              _buildCompletionMessage(),
          ],
        ),
      ),
    );
  }

  Widget _buildBoxLetters() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      alignment: WrapAlignment.center,
      children: List.generate(
        viewState.value!.currentGame!.splitLetters.length,
        (index) => _buildLetterTile(
          '',
          isSpace: viewState.value!.currentGame!.splitLetters[index] == ' ',
          () {},
          Color(0xffFFDEC3),
        ),
      ),
    );
  }

  Widget _buildSelectedLetters() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      alignment: WrapAlignment.center,
      children: List.generate(
        viewState.value!.currentGame!.selectedLetters.length,
        (index) => _buildLetterTile(
          viewState.value!.currentGame!.selectedLetters[index],
          () => viewModel.unselectLetter(index),
          // Colors.blue.shade100,
          Color(0xffF33928),
        ),
      ),
    );
  }

  Widget _buildScrambledLetters() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      alignment: WrapAlignment.center,
      children: List.generate(
        viewState.value!.currentGame!.scrambledLetters.length,
        (index) => _buildLetterTile(
          viewState.value!.currentGame!.scrambledLetters[index],
          () => viewModel.selectLetter(index),
          // Colors.amber.shade100,
          Color(0xff998E8D),
        ),
      ),
    );
  }

  Widget _buildLetterTile(
    String letter,
    VoidCallback onTap,
    Color color, {
    bool isSpace = false,
  }) {
    return isSpace
        ? SizedBox(width: 50, height: 50)
        : GestureDetector(
            onTap: onTap,
            child: Container(
              width: 50,
              height: 50,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.2),
                    spreadRadius: 1,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                letter,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          );
  }

  Widget _buildCompletionMessage() {
    bool isCorrect = viewState.value!.currentGame!.score > 0;
    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isCorrect ? Colors.green.shade100 : Colors.red.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            isCorrect ? 'Correct!' : 'Incorrect!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isCorrect ? Colors.green : Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '+${viewState.value!.currentGame!.score} points',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton(
            onPressed: viewModel.resetGame,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text(
              'Reset',
              style: TextStyle(fontSize: 16, color: Colors.white),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              viewModel.exitGame();
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text(
              'Exit',
              style: TextStyle(fontSize: 16, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultStage() {
    _playAudio("sounds/memory_flash/score.mp3");
    return Column(
      children: [
        const SizedBox(height: 15),
        _buildStageTitle(context.loc.memory_flash_result_desc),
        const SizedBox(height: 20),
        _buildResultWords(),
      ],
    );
  }

  Widget _buildStageTitle(String title) {
    return Text(
      title,
      style: Theme.of(
        context,
      ).textTheme.headlineLarge!.copyWith(color: Colors.white),
      textScaler: const TextScaler.linear(0.8),
    );
  }

  Widget _buildResultWords() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: viewState.value!.wordList
          .map((item) => _buildResultWord(item.answer))
          .toList(),
    );
  }

  Widget _buildResultWord(String word) {
    final isSelected = false;

    return Stack(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [_buildResultWordButton(word, isSelected)],
        ),
      ],
    );
  }

  Widget _buildResultWordButton(String word, bool isSelected) {
    return ElevatedButton(
      onPressed: () {},
      style: _getWordChoiceStyle(isSelected),
      child: Text(
        word,
        style: _getWordChoiceTextStyle(isSelected),
        textScaler: const TextScaler.linear(0.8),
      ),
    );
  }

  ButtonStyle _getWordChoiceStyle(bool isSelected) {
    return ElevatedButton.styleFrom(
      backgroundColor: isSelected
          ? const Color(0xffE41A19)
          : const Color(0xffFFDAD6),
      minimumSize: const Size(120, 50),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
        side: const BorderSide(color: Colors.white),
      ),
    );
  }

  TextStyle? _getWordChoiceTextStyle(bool isSelected) {
    return Theme.of(context).textTheme.headlineSmall?.copyWith(
      color: isSelected ? Colors.white : const Color(0xffAE202E),
    );
  }

  Widget _buildFinishedStage() {
    _playAudio('sounds/memory_flash/Hasil Score Keluar.MP3');

    return ListView(
      children: [
        const SizedBox(height: 40),
        _buildScoreContainer(),
        const SizedBox(height: 18),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildScoreContainer() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 20),
      padding: const EdgeInsets.symmetric(vertical: 46, horizontal: 42),
      decoration: _getScoreContainerDecoration(),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildScoreSection(
            context.loc.topic_complete,
            context.loc.congratulations,
          ),
          _buildScoreSection(context.loc.level_complete, _getScoreText()),
        ],
      ),
    );
  }

  BoxDecoration _getScoreContainerDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(16),
      gradient: const LinearGradient(
        begin: Alignment.centerRight,
        end: Alignment.centerLeft,
        colors: [Color(0xff8B000A), Color(0xffF33928), Color(0xffFF8D46)],
      ),
    );
  }

  Widget _buildScoreSection(String title, String subtitle) {
    return Column(
      children: [
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.headlineLarge?.copyWith(color: Colors.white),
          textScaler: const TextScaler.linear(0.8),
        ),
        _buildStarRating(),
        _buildScoreLabel(subtitle),
      ],
    );
  }

  Widget _buildStarRating() {
    final starCount =
        (viewState.value!.totalScore / viewState.value!.wordList.length * 0.25)
            .round();
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 14),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(starCount, (index) {
          return Image.asset(
            '$assetImageGames/memory_flash/Star2-Game.png',
            height: 50,
            width: 50,
          );
        }),
      ),
    );
  }

  Widget _buildScoreLabel(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 10),
      decoration: _getScoreLabelDecoration(),
      child: Text(
        text,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(color: Colors.white),
        textScaler: const TextScaler.linear(0.8),
      ),
    );
  }

  BoxDecoration _getScoreLabelDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(20),
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xffFA3236), Color(0xffD9353C), Color(0xff8C1412)],
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: .3),
          blurRadius: 3,
          offset: const Offset(2, 3),
        ),
      ],
    );
  }

  String _getScoreText() {
    return '${context.loc.score}: ${viewState.value!.totalScore}/${viewState.value!.wordList.length}  🤩✨';
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildActionButton(context.loc.replay, _handleReplay),
        const SizedBox(width: 84),
        _buildActionButton(context.loc.next, _handleNext),
      ],
    );
  }

  Widget _buildActionButton(String text, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 10),
        decoration: _getActionButtonDecoration(),
        child: Text(
          text,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(color: Colors.white),
        ),
      ),
    );
  }

  BoxDecoration _getActionButtonDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(4),
      gradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xffF36341), Color(0xffC70039)],
      ),
    );
  }

  void _handleReplay() {
    _isAudioPlayed = false;
    viewModel.resetGame();
  }

  void _handleNext() {
    // Mark game as completed using the game completion controller
    ref.read(gameCompletionControllerProvider.notifier).markGameCompleted();
    // Navigate back without returning a value
    context.pop();
  }

  Widget _buildGameOver() {
    _playAudio('sounds/memory_flash/Game Over.MP3');
    return Center(
      child: Image.asset(
        '$assetImageGames/memory_flash/Game over.png',
        height: MediaQuery.of(context).size.height * 0.88,
      ),
    );
  }

  // Utility methods
  void _playAudio(String path) async {
    await _player.stop();
    _player.play(AssetSource(path));
  }
}
