// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'word_scramble_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WordScrambleState {

 GameStage get gameStage; List<WordScramble> get wordList; int get currentWordIndex; WordScramble? get currentGame; int get totalScore; bool get isGameCompleted; MemoryFlashTopic? get topic; int get currentTopicIndex;
/// Create a copy of WordScrambleState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WordScrambleStateCopyWith<WordScrambleState> get copyWith => _$WordScrambleStateCopyWithImpl<WordScrambleState>(this as WordScrambleState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WordScrambleState&&(identical(other.gameStage, gameStage) || other.gameStage == gameStage)&&const DeepCollectionEquality().equals(other.wordList, wordList)&&(identical(other.currentWordIndex, currentWordIndex) || other.currentWordIndex == currentWordIndex)&&(identical(other.currentGame, currentGame) || other.currentGame == currentGame)&&(identical(other.totalScore, totalScore) || other.totalScore == totalScore)&&(identical(other.isGameCompleted, isGameCompleted) || other.isGameCompleted == isGameCompleted)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.currentTopicIndex, currentTopicIndex) || other.currentTopicIndex == currentTopicIndex));
}


@override
int get hashCode => Object.hash(runtimeType,gameStage,const DeepCollectionEquality().hash(wordList),currentWordIndex,currentGame,totalScore,isGameCompleted,topic,currentTopicIndex);

@override
String toString() {
  return 'WordScrambleState(gameStage: $gameStage, wordList: $wordList, currentWordIndex: $currentWordIndex, currentGame: $currentGame, totalScore: $totalScore, isGameCompleted: $isGameCompleted, topic: $topic, currentTopicIndex: $currentTopicIndex)';
}


}

/// @nodoc
abstract mixin class $WordScrambleStateCopyWith<$Res>  {
  factory $WordScrambleStateCopyWith(WordScrambleState value, $Res Function(WordScrambleState) _then) = _$WordScrambleStateCopyWithImpl;
@useResult
$Res call({
 GameStage gameStage, List<WordScramble> wordList, int currentWordIndex, WordScramble? currentGame, int totalScore, bool isGameCompleted, MemoryFlashTopic? topic, int currentTopicIndex
});


$WordScrambleCopyWith<$Res>? get currentGame;$MemoryFlashTopicCopyWith<$Res>? get topic;

}
/// @nodoc
class _$WordScrambleStateCopyWithImpl<$Res>
    implements $WordScrambleStateCopyWith<$Res> {
  _$WordScrambleStateCopyWithImpl(this._self, this._then);

  final WordScrambleState _self;
  final $Res Function(WordScrambleState) _then;

/// Create a copy of WordScrambleState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? gameStage = null,Object? wordList = null,Object? currentWordIndex = null,Object? currentGame = freezed,Object? totalScore = null,Object? isGameCompleted = null,Object? topic = freezed,Object? currentTopicIndex = null,}) {
  return _then(_self.copyWith(
gameStage: null == gameStage ? _self.gameStage : gameStage // ignore: cast_nullable_to_non_nullable
as GameStage,wordList: null == wordList ? _self.wordList : wordList // ignore: cast_nullable_to_non_nullable
as List<WordScramble>,currentWordIndex: null == currentWordIndex ? _self.currentWordIndex : currentWordIndex // ignore: cast_nullable_to_non_nullable
as int,currentGame: freezed == currentGame ? _self.currentGame : currentGame // ignore: cast_nullable_to_non_nullable
as WordScramble?,totalScore: null == totalScore ? _self.totalScore : totalScore // ignore: cast_nullable_to_non_nullable
as int,isGameCompleted: null == isGameCompleted ? _self.isGameCompleted : isGameCompleted // ignore: cast_nullable_to_non_nullable
as bool,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as MemoryFlashTopic?,currentTopicIndex: null == currentTopicIndex ? _self.currentTopicIndex : currentTopicIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of WordScrambleState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WordScrambleCopyWith<$Res>? get currentGame {
    if (_self.currentGame == null) {
    return null;
  }

  return $WordScrambleCopyWith<$Res>(_self.currentGame!, (value) {
    return _then(_self.copyWith(currentGame: value));
  });
}/// Create a copy of WordScrambleState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MemoryFlashTopicCopyWith<$Res>? get topic {
    if (_self.topic == null) {
    return null;
  }

  return $MemoryFlashTopicCopyWith<$Res>(_self.topic!, (value) {
    return _then(_self.copyWith(topic: value));
  });
}
}


/// Adds pattern-matching-related methods to [WordScrambleState].
extension WordScrambleStatePatterns on WordScrambleState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WordScrambleState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WordScrambleState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WordScrambleState value)  $default,){
final _that = this;
switch (_that) {
case _WordScrambleState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WordScrambleState value)?  $default,){
final _that = this;
switch (_that) {
case _WordScrambleState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( GameStage gameStage,  List<WordScramble> wordList,  int currentWordIndex,  WordScramble? currentGame,  int totalScore,  bool isGameCompleted,  MemoryFlashTopic? topic,  int currentTopicIndex)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WordScrambleState() when $default != null:
return $default(_that.gameStage,_that.wordList,_that.currentWordIndex,_that.currentGame,_that.totalScore,_that.isGameCompleted,_that.topic,_that.currentTopicIndex);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( GameStage gameStage,  List<WordScramble> wordList,  int currentWordIndex,  WordScramble? currentGame,  int totalScore,  bool isGameCompleted,  MemoryFlashTopic? topic,  int currentTopicIndex)  $default,) {final _that = this;
switch (_that) {
case _WordScrambleState():
return $default(_that.gameStage,_that.wordList,_that.currentWordIndex,_that.currentGame,_that.totalScore,_that.isGameCompleted,_that.topic,_that.currentTopicIndex);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( GameStage gameStage,  List<WordScramble> wordList,  int currentWordIndex,  WordScramble? currentGame,  int totalScore,  bool isGameCompleted,  MemoryFlashTopic? topic,  int currentTopicIndex)?  $default,) {final _that = this;
switch (_that) {
case _WordScrambleState() when $default != null:
return $default(_that.gameStage,_that.wordList,_that.currentWordIndex,_that.currentGame,_that.totalScore,_that.isGameCompleted,_that.topic,_that.currentTopicIndex);case _:
  return null;

}
}

}

/// @nodoc


class _WordScrambleState implements WordScrambleState {
   _WordScrambleState({this.gameStage = GameStage.loading, final  List<WordScramble> wordList = const [], this.currentWordIndex = 0, this.currentGame, this.totalScore = 0, this.isGameCompleted = false, this.topic, this.currentTopicIndex = 0}): _wordList = wordList;
  

@override@JsonKey() final  GameStage gameStage;
 final  List<WordScramble> _wordList;
@override@JsonKey() List<WordScramble> get wordList {
  if (_wordList is EqualUnmodifiableListView) return _wordList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_wordList);
}

@override@JsonKey() final  int currentWordIndex;
@override final  WordScramble? currentGame;
@override@JsonKey() final  int totalScore;
@override@JsonKey() final  bool isGameCompleted;
@override final  MemoryFlashTopic? topic;
@override@JsonKey() final  int currentTopicIndex;

/// Create a copy of WordScrambleState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WordScrambleStateCopyWith<_WordScrambleState> get copyWith => __$WordScrambleStateCopyWithImpl<_WordScrambleState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WordScrambleState&&(identical(other.gameStage, gameStage) || other.gameStage == gameStage)&&const DeepCollectionEquality().equals(other._wordList, _wordList)&&(identical(other.currentWordIndex, currentWordIndex) || other.currentWordIndex == currentWordIndex)&&(identical(other.currentGame, currentGame) || other.currentGame == currentGame)&&(identical(other.totalScore, totalScore) || other.totalScore == totalScore)&&(identical(other.isGameCompleted, isGameCompleted) || other.isGameCompleted == isGameCompleted)&&(identical(other.topic, topic) || other.topic == topic)&&(identical(other.currentTopicIndex, currentTopicIndex) || other.currentTopicIndex == currentTopicIndex));
}


@override
int get hashCode => Object.hash(runtimeType,gameStage,const DeepCollectionEquality().hash(_wordList),currentWordIndex,currentGame,totalScore,isGameCompleted,topic,currentTopicIndex);

@override
String toString() {
  return 'WordScrambleState(gameStage: $gameStage, wordList: $wordList, currentWordIndex: $currentWordIndex, currentGame: $currentGame, totalScore: $totalScore, isGameCompleted: $isGameCompleted, topic: $topic, currentTopicIndex: $currentTopicIndex)';
}


}

/// @nodoc
abstract mixin class _$WordScrambleStateCopyWith<$Res> implements $WordScrambleStateCopyWith<$Res> {
  factory _$WordScrambleStateCopyWith(_WordScrambleState value, $Res Function(_WordScrambleState) _then) = __$WordScrambleStateCopyWithImpl;
@override @useResult
$Res call({
 GameStage gameStage, List<WordScramble> wordList, int currentWordIndex, WordScramble? currentGame, int totalScore, bool isGameCompleted, MemoryFlashTopic? topic, int currentTopicIndex
});


@override $WordScrambleCopyWith<$Res>? get currentGame;@override $MemoryFlashTopicCopyWith<$Res>? get topic;

}
/// @nodoc
class __$WordScrambleStateCopyWithImpl<$Res>
    implements _$WordScrambleStateCopyWith<$Res> {
  __$WordScrambleStateCopyWithImpl(this._self, this._then);

  final _WordScrambleState _self;
  final $Res Function(_WordScrambleState) _then;

/// Create a copy of WordScrambleState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? gameStage = null,Object? wordList = null,Object? currentWordIndex = null,Object? currentGame = freezed,Object? totalScore = null,Object? isGameCompleted = null,Object? topic = freezed,Object? currentTopicIndex = null,}) {
  return _then(_WordScrambleState(
gameStage: null == gameStage ? _self.gameStage : gameStage // ignore: cast_nullable_to_non_nullable
as GameStage,wordList: null == wordList ? _self._wordList : wordList // ignore: cast_nullable_to_non_nullable
as List<WordScramble>,currentWordIndex: null == currentWordIndex ? _self.currentWordIndex : currentWordIndex // ignore: cast_nullable_to_non_nullable
as int,currentGame: freezed == currentGame ? _self.currentGame : currentGame // ignore: cast_nullable_to_non_nullable
as WordScramble?,totalScore: null == totalScore ? _self.totalScore : totalScore // ignore: cast_nullable_to_non_nullable
as int,isGameCompleted: null == isGameCompleted ? _self.isGameCompleted : isGameCompleted // ignore: cast_nullable_to_non_nullable
as bool,topic: freezed == topic ? _self.topic : topic // ignore: cast_nullable_to_non_nullable
as MemoryFlashTopic?,currentTopicIndex: null == currentTopicIndex ? _self.currentTopicIndex : currentTopicIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of WordScrambleState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WordScrambleCopyWith<$Res>? get currentGame {
    if (_self.currentGame == null) {
    return null;
  }

  return $WordScrambleCopyWith<$Res>(_self.currentGame!, (value) {
    return _then(_self.copyWith(currentGame: value));
  });
}/// Create a copy of WordScrambleState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MemoryFlashTopicCopyWith<$Res>? get topic {
    if (_self.topic == null) {
    return null;
  }

  return $MemoryFlashTopicCopyWith<$Res>(_self.topic!, (value) {
    return _then(_self.copyWith(topic: value));
  });
}
}

// dart format on
