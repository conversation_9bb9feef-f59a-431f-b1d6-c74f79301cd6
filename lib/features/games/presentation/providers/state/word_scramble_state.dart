// observe_recall_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/games/domain/models/game_word.dart';

part 'word_scramble_state.freezed.dart';

enum GameStage { topic, loading, game, result, finished, gameOver }

@freezed
sealed class WordScrambleState with _$WordScrambleState {
  factory WordScrambleState({
    @Default(GameStage.loading) GameStage gameStage,
    @Default([]) List<WordScramble> wordList,
    @Default(0) int currentWordIndex,
    WordScramble? currentGame,
    @Default(0) int totalScore,
    @Default(false) bool isGameCompleted,
    MemoryFlashTopic? topic,
    @Default(0) int currentTopicIndex,
  }) = _WordScrambleState;
}
