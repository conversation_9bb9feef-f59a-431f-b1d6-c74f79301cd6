// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'word_scramble_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$wordScrambleControllerHash() =>
    r'49a26c15d0f9e51440d138a81bca67c18c96609c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$WordScrambleController
    extends BuildlessAutoDisposeAsyncNotifier<WordScrambleState> {
  late final String? topic;

  FutureOr<WordScrambleState> build({String? topic});
}

/// See also [WordScrambleController].
@ProviderFor(WordScrambleController)
const wordScrambleControllerProvider = WordScrambleControllerFamily();

/// See also [WordScrambleController].
class WordScrambleControllerFamily
    extends Family<AsyncValue<WordScrambleState>> {
  /// See also [WordScrambleController].
  const WordScrambleControllerFamily();

  /// See also [WordScrambleController].
  WordScrambleControllerProvider call({String? topic}) {
    return WordScrambleControllerProvider(topic: topic);
  }

  @override
  WordScrambleControllerProvider getProviderOverride(
    covariant WordScrambleControllerProvider provider,
  ) {
    return call(topic: provider.topic);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'wordScrambleControllerProvider';
}

/// See also [WordScrambleController].
class WordScrambleControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          WordScrambleController,
          WordScrambleState
        > {
  /// See also [WordScrambleController].
  WordScrambleControllerProvider({String? topic})
    : this._internal(
        () => WordScrambleController()..topic = topic,
        from: wordScrambleControllerProvider,
        name: r'wordScrambleControllerProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$wordScrambleControllerHash,
        dependencies: WordScrambleControllerFamily._dependencies,
        allTransitiveDependencies:
            WordScrambleControllerFamily._allTransitiveDependencies,
        topic: topic,
      );

  WordScrambleControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.topic,
  }) : super.internal();

  final String? topic;

  @override
  FutureOr<WordScrambleState> runNotifierBuild(
    covariant WordScrambleController notifier,
  ) {
    return notifier.build(topic: topic);
  }

  @override
  Override overrideWith(WordScrambleController Function() create) {
    return ProviderOverride(
      origin: this,
      override: WordScrambleControllerProvider._internal(
        () => create()..topic = topic,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        topic: topic,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    WordScrambleController,
    WordScrambleState
  >
  createElement() {
    return _WordScrambleControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WordScrambleControllerProvider && other.topic == topic;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, topic.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WordScrambleControllerRef
    on AutoDisposeAsyncNotifierProviderRef<WordScrambleState> {
  /// The parameter `topic` of this provider.
  String? get topic;
}

class _WordScrambleControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          WordScrambleController,
          WordScrambleState
        >
    with WordScrambleControllerRef {
  _WordScrambleControllerProviderElement(super.provider);

  @override
  String? get topic => (origin as WordScrambleControllerProvider).topic;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
