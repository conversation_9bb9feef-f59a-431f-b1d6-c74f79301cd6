import 'dart:async';
import 'dart:math';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/games/domain/models/game_word.dart';
import 'package:selfeng/features/games/domain/providers/game_provider.dart';
import 'package:selfeng/features/games/domain/repositories/game_repository.dart';
import 'package:selfeng/features/games/presentation/providers/state/word_scramble_state.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/timer_controller.dart';

part 'word_scramble_controller.g.dart';

@riverpod
class WordScrambleController extends _$WordScrambleController {
  late GameRepository gameRepository;
  late TimerController viewTimerModel;
  @override
  FutureOr<WordScrambleState> build({String? topic}) async {
    viewTimerModel = ref.watch(timerControllerProvider.notifier);
    gameRepository = ref.watch(gameRepositoryProvider);

    _init();

    return WordScrambleState();
  }

  Future<void> _init() async {
    try {
      await Future.delayed(Duration(seconds: 1));

      state = AsyncLoading();

      // Check if topic is null before using it
      if (topic == null) {
        state = AsyncError('Topic is required', StackTrace.current);
        return;
      }

      final contents = await gameRepository.getTopic(references: topic!);
      contents.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          try {
            state = AsyncData(WordScrambleState(topic: data));
            selectTopic();
          } catch (error, stackTrace) {
            state = AsyncError(
              'Failed to initialize game state: $error',
              stackTrace,
            );
          }
        },
      );
    } catch (error, stackTrace) {
      state = AsyncError('Initialization failed: $error', stackTrace);
    }
  }

  void selectTopic() {
    try {
      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncData(currentState.copyWith(gameStage: GameStage.loading));
        viewTimerModel.setTotalSecond(seconds: 3);
        viewTimerModel.startTimer();
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to select topic: $error', stackTrace);
    }
  }

  Future<void> loadGameContent() async {
    try {
      // if (isLoaded) return;
      // isLoaded = true;

      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError('Game state is not initialized', StackTrace.current);
        return;
      }

      if (currentState.wordList.isNotEmpty) {
        startGame();
        return;
      }

      // Check if topic is null before using it
      if (topic == null) {
        state = AsyncError(
          'Topic is required for loading content',
          StackTrace.current,
        );
        return;
      }

      final contents = await gameRepository.getListTextWordScramble(
        referencesId: topic!,
      );

      contents.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          try {
            if (data.isEmpty) {
              state = AsyncError(
                "The topic that you select not available, please choose another topic.",
                StackTrace.current,
              );
              // Wait a moment then reset to topic selection
              try {
                await Future.delayed(const Duration(seconds: 2), () {});
              } catch (delayError) {
                // Handle delay errors silently as they're not critical
              }
              return;
            }

            final stateValue = state.valueOrNull;
            if (stateValue != null) {
              state = AsyncData(stateValue.copyWith(wordList: data));
              startGame();
            }
            print('loadGameContent: ${state.value!.wordList.length}');
          } catch (error, stackTrace) {
            state = AsyncError(
              'Failed to process game content: $error',
              stackTrace,
            );
          }
        },
      );
    } catch (error, stackTrace) {
      // isLoaded = false; // Reset loading flag on error
      state = AsyncError('Failed to load game content: $error', stackTrace);
    }
  }

  void startGame() {
    try {
      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError(
          'Cannot start game: state is null',
          StackTrace.current,
        );
        return;
      }
      WordScramble tempGame = _initializeGame();

      state = AsyncData(
        currentState.copyWith(gameStage: GameStage.game, currentGame: tempGame),
      );

      viewTimerModel.setTotalSecond(
        seconds: 25,
        // updatedState.memoryFlash[updatedState.currentLevelIndex].countdown ??
        // 21,
      );
      viewTimerModel.startTimer();
    } catch (error, stackTrace) {
      state = AsyncError('Failed to start game: $error', stackTrace);
    }
  }

  WordScramble _initializeGame() {
    final currentState = state.valueOrNull;
    if (currentState == null || currentState.wordList.isEmpty) {
      state = AsyncError(
        'Cannot initialize game: word list is empty',
        StackTrace.current,
      );
      return WordScramble();
    }

    final currentWord = currentState.wordList[currentState.currentWordIndex];
    final scrambledLetters = _scrambleWord(currentWord.answer);

    return WordScramble(
      word: currentWord.answer,
      splitLetters: currentWord.answer.split(''),
      scrambledLetters: scrambledLetters,
      selectedLetters: [],
    );
  }

  List<String> _scrambleWord(String word) {
    final letters = word.split('');
    letters.shuffle(Random());
    return letters;
  }

  void selectLetter(int index) {
    final currentGame = state.value!.currentGame;
    final scrambledLetters = List<String>.from(currentGame!.scrambledLetters);
    final selectedLetters = List<String>.from(currentGame.selectedLetters);

    if (index >= 0 && index < scrambledLetters.length) {
      final letter = scrambledLetters[index];
      selectedLetters.add(letter);
      scrambledLetters.removeAt(index);

      final updatedGame = currentGame.copyWith(
        scrambledLetters: scrambledLetters,
        selectedLetters: selectedLetters,
      );

      state = AsyncData(state.value!.copyWith(currentGame: updatedGame));
      checkWord();
    }
  }

  void unselectLetter(int index) {
    final currentGame = state.value!.currentGame;
    final scrambledLetters = List<String>.from(currentGame!.scrambledLetters);
    final selectedLetters = List<String>.from(currentGame.selectedLetters);

    if (index >= 0 && index < selectedLetters.length) {
      final letter = selectedLetters[index];
      scrambledLetters.add(letter);
      selectedLetters.removeAt(index);

      final updatedGame = currentGame.copyWith(
        scrambledLetters: scrambledLetters,
        selectedLetters: selectedLetters,
      );

      state = AsyncData(state.value!.copyWith(currentGame: updatedGame));
    }
  }

  void checkWord() {
    final currentGame = state.value!.currentGame;
    final selectedWord = currentGame!.selectedLetters.join('');

    if (selectedWord.length == currentGame.word.length) {
      int score = 0;
      if (selectedWord == currentGame.word) {
        score = state.value!.wordList[state.value!.currentWordIndex].point ?? 0;
      }
      final updatedGame = currentGame.copyWith(isCompleted: true, score: score);

      final totalScore = (state.value?.totalScore ?? 0) + score;
      state = AsyncData(
        state.value!.copyWith(currentGame: updatedGame, totalScore: totalScore),
      );

      // Move to next word if available
      if (state.value!.currentWordIndex < state.value!.wordList.length - 1) {
        Future.delayed(const Duration(seconds: 1), () {
          nextWord();
        });
      } else {
        Future.delayed(const Duration(seconds: 1), () {
          submitAnswers();
        });
      }
    }
  }

  void nextWord() {
    final nextIndex = state.value!.currentWordIndex + 1;
    if (nextIndex < state.value!.wordList.length) {
      final nextWord = state.value!.wordList[nextIndex].answer;
      final scrambledLetters = _scrambleWord(nextWord);

      final newGame = WordScramble(
        word: nextWord,
        splitLetters: nextWord.split(''),
        scrambledLetters: scrambledLetters,
        selectedLetters: [],
      );

      state = AsyncData(
        state.value!.copyWith(
          currentWordIndex: nextIndex,
          currentGame: newGame,
        ),
      );
    }
  }

  void resetGame() {
    state = AsyncData(
      state.value!.copyWith(
        currentWordIndex: 0,
        currentGame: null,
        totalScore: 0,
        isGameCompleted: false,
      ),
    );
    selectTopic();
  }

  void finishGame() {
    try {
      final currentState = state.valueOrNull;

      viewTimerModel.stopTimer();
      if (currentState != null) {
        state = AsyncData(currentState.copyWith(gameStage: GameStage.finished));
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to finish game: $error', stackTrace);
    }
  }

  void gameOver() {
    try {
      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncData(currentState.copyWith(gameStage: GameStage.gameOver));
        Future.delayed(
          Duration(seconds: 5),
        ).then((e) => submitAnswers()).catchError((error) {
          state = AsyncError(
            'Game over delay failed: $error',
            StackTrace.current,
          );
        });
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to trigger game over: $error', stackTrace);
    }
  }

  Future<void> submitAnswers() async {
    try {
      viewTimerModel.stopTimer();

      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError(
          'Cannot submit answers: state is null',
          StackTrace.current,
        );
        return;
      }

      state = AsyncData(currentState.copyWith(gameStage: GameStage.result));

      // Check bounds before accessing array
      if (currentState.wordList.isNotEmpty) {
        await gameRepository.saveResultWordScramble(
          references: currentState.wordList[0].references ?? '',
          score: currentState.totalScore,
        );
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to submit answers: $error', stackTrace);
    }
  }

  void exitGame() {
    // Stop the game if it's running
  }
}
