// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'topic_game_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$topicGameControllerHash() =>
    r'd19e1de3d43e85fdc960eedb723cdae619677b3c';

/// See also [TopicGameController].
@ProviderFor(TopicGameController)
final topicGameControllerProvider =
    AsyncNotifierProvider<TopicGameController, TopicGameState>.internal(
      TopicGameController.new,
      name: r'topicGameControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$topicGameControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TopicGameController = AsyncNotifier<TopicGameState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
