import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:selfeng/features/games/domain/models/game_word.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

abstract class GameRepository {
  Future<Either<AppException, List<MemoryFlashTopic>>> getTopicsMemoryFlash();

  Future<Either<AppException, MemoryFlashTopic>> getTopic({
    required String references,
  });

  Future<Either<AppException, List<MemoryFlash>>> getListTextMemoryFlash({
    required String referencesId,
  });

  Future<Either<AppException, String>> saveResultMemoryFlash({
    required String references,
    required int score,
  });

  Future<Either<AppException, List<WordScramble>>> getListTextWordScramble({
    required String referencesId,
  });

  Future<Either<AppException, String>> saveResultWordScramble({
    required String references,
    required int score,
  });
}

class GameRepositoryImpl extends GameRepository {
  // final Firebase Firestore dataSource;
  final FirestoreServiceRepository dataSource;

  GameRepositoryImpl(this.dataSource);

  @override
  Future<Either<AppException, List<MemoryFlashTopic>>>
  getTopicsMemoryFlash() async {
    try {
      final QuerySnapshot<Map<String, dynamic>> result =
          await dataSource.fireStore
              .collection('/fun-quiz/game_topic/list')
              .orderBy('order')
              .get();

      final docs =
          result.docs.map((doc) {
            MemoryFlashTopic docData = MemoryFlashTopic.fromJson(doc.data());
            docData = docData.copyWith(references: doc.id);
            return docData;
          }).toList();

      return Right(docs);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch Chapter Index',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, MemoryFlashTopic>> getTopic({
    required String references,
  }) async {
    try {
      final DocumentSnapshot<Map<String, dynamic>> result =
          await dataSource.fireStore
              .collection('/fun-quiz/game_topic/list')
              .doc(references)
              .get();

      return Right(MemoryFlashTopic.fromJson(result.data()!));
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch Chapter Index',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, List<MemoryFlash>>> getListTextMemoryFlash({
    required String referencesId,
  }) async {
    try {
      final QuerySnapshot<Map<String, dynamic>> result =
          await dataSource.fireStore
              .collection('/fun-quiz/memory_flash/list/')
              .where('topic', isEqualTo: referencesId)
              .orderBy('order')
              .get();
      final docs =
          result.docs.map((doc) {
            MemoryFlash docData = MemoryFlash.fromJson(doc.data());
            docData = docData.copyWith(references: doc.id);
            return docData;
          }).toList();

      return Right(docs);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch Chapter Index',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, String>> saveResultMemoryFlash({
    required String references,
    required int score,
  }) async {
    try {
      print(saveResultMemoryFlash);
      await dataSource
          .dataUser()
          .collection('game_score')
          .doc('memory_flash')
          .collection('score')
          .doc('references')
          .set({'score': score});

      // WordObject data = WordObject.fromJson(result.data()!);

      return Right('Success');
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch Chapter Index',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, List<WordScramble>>> getListTextWordScramble({
    required String referencesId,
  }) async {
    try {
      final QuerySnapshot<Map<String, dynamic>> result =
          await dataSource.fireStore
              .collection('/fun-quiz/word_scramble/list/')
              .where('topic', isEqualTo: referencesId)
              .orderBy('order')
              .get();
      final docs =
          result.docs.map((doc) {
            WordScramble docData = WordScramble.fromJson(doc.data());
            docData = docData.copyWith(references: doc.id);
            return docData;
          }).toList();

      return Right(docs);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch Chapter Index',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  Future<Either<AppException, String>> saveResultWordScramble({
    required String references,
    required int score,
  }) async {
    try {
      print(saveResultMemoryFlash);
      await dataSource
          .dataUser()
          .collection('game_score')
          .doc('word_scramble')
          .collection('score')
          .doc('references')
          .set({'score': score});

      // WordObject data = WordObject.fromJson(result.data()!);

      return Right('Success');
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Fetch Chapter Index',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }
}
