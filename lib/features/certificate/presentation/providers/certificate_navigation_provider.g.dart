// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'certificate_navigation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$certificateNavigationControllerHash() =>
    r'fae6acdb16f17948b5fbe9b910a55878729c32e0';

/// Provider to manage certificate navigation data
/// This allows passing complex objects through GoRouter by storing them in state
///
/// Copied from [CertificateNavigationController].
@ProviderFor(CertificateNavigationController)
final certificateNavigationControllerProvider =
    NotifierProvider<
      CertificateNavigationController,
      CertificateNavigationData?
    >.internal(
      CertificateNavigationController.new,
      name: r'certificateNavigationControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$certificateNavigationControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CertificateNavigationController =
    Notifier<CertificateNavigationData?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
