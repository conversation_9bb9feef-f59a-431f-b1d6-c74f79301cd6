// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bookmark_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bookmarkRepositoryHash() =>
    r'a74cff7b2647e4fe5ebabeda05251eab3aaa4018';

/// See also [bookmarkRepository].
@ProviderFor(bookmarkRepository)
final bookmarkRepositoryProvider =
    AutoDisposeProvider<BookmarkRepository>.internal(
      bookmarkRepository,
      name: r'bookmarkRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bookmarkRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BookmarkRepositoryRef = AutoDisposeProviderRef<BookmarkRepository>;
String _$bookmarkListHash() => r'ccfeb74c178a5b1add0f291fe205063111adc501';

/// See also [BookmarkList].
@ProviderFor(BookmarkList)
final bookmarkListProvider =
    AutoDisposeAsyncNotifierProvider<BookmarkList, List<Bookmark>>.internal(
      BookmarkList.new,
      name: r'bookmarkListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bookmarkListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BookmarkList = AutoDisposeAsyncNotifier<List<Bookmark>>;
String _$bookmarkFilterHash() => r'b1f856401f024510ef96b104c3c36f3b71261b9f';

/// See also [BookmarkFilter].
@ProviderFor(BookmarkFilter)
final bookmarkFilterProvider =
    AutoDisposeNotifierProvider<BookmarkFilter, String?>.internal(
      BookmarkFilter.new,
      name: r'bookmarkFilterProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bookmarkFilterHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BookmarkFilter = AutoDisposeNotifier<String?>;
String _$bookmarkSearchHash() => r'cd0651df524e1560218eba01c66f9b5a9a38df45';

/// See also [BookmarkSearch].
@ProviderFor(BookmarkSearch)
final bookmarkSearchProvider =
    AutoDisposeNotifierProvider<BookmarkSearch, String>.internal(
      BookmarkSearch.new,
      name: r'bookmarkSearchProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bookmarkSearchHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$BookmarkSearch = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
