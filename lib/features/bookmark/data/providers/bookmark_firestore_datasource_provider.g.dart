// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bookmark_firestore_datasource_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bookmarkFirestoreDatasourceHash() =>
    r'baa24edd4252414ad1fa2614eb29f97dd6e5b57b';

/// See also [bookmarkFirestoreDatasource].
@ProviderFor(bookmarkFirestoreDatasource)
final bookmarkFirestoreDatasourceProvider =
    AutoDisposeProvider<BookmarkFirestoreDatasource>.internal(
      bookmarkFirestoreDatasource,
      name: r'bookmarkFirestoreDatasourceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bookmarkFirestoreDatasourceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BookmarkFirestoreDatasourceRef =
    AutoDisposeProviderRef<BookmarkFirestoreDatasource>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
