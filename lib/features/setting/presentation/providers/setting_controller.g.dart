// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'setting_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$settingControllerHash() => r'b3de08409783df905f40c4450ee8cdc173ba8bd1';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [SettingController].
@ProviderFor(SettingController)
final settingControllerProvider =
    AutoDisposeAsyncNotifierProvider<SettingController, SettingState>.internal(
      SettingController.new,
      name: r'settingC<PERSON>roller<PERSON>rovider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$settingControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SettingController = AutoDisposeAsyncNotifier<SettingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
