// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tab_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$tabControllerHash() => r'89fbffd2772bd75d3f0719c11d96547e253c00de';

/// See also [TabController].
@ProviderFor(TabController)
final tabControllerProvider =
    AutoDisposeNotifierProvider<TabController, int>.internal(
      TabController.new,
      name: r'tabControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$tabControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TabController = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
