// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_material_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$learningMaterialControllerHash() =>
    r'24d35477bbd0806815465acfe5ba26af601299c1';

/// See also [LearningMaterialController].
@ProviderFor(LearningMaterialController)
final learningMaterialControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      LearningMaterialController,
      LearningMaterialState
    >.internal(
      LearningMaterialController.new,
      name: r'learningMaterialControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$learningMaterialControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$LearningMaterialController =
    AutoDisposeAsyncNotifier<LearningMaterialState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
