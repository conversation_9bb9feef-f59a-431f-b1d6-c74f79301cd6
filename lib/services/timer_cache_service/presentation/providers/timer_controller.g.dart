// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'timer_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$timerControllerHash() => r'c5dddeb994a662938817ed57a8aee51d7ddf8e5b';

/// A Riverpod controller that manages timer functionality with state management
///
/// Copied from [TimerController].
@ProviderFor(TimerController)
final timerControllerProvider =
    AutoDisposeAsyncNotifierProvider<TimerController, TimerState>.internal(
      TimerController.new,
      name: r'timer<PERSON><PERSON><PERSON><PERSON><PERSON>rovider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$timerControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$TimerController = AutoDisposeAsyncNotifier<TimerState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
