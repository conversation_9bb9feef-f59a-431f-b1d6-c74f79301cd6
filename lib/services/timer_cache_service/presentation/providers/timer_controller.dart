import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'state/timer_state.dart';

part 'timer_controller.g.dart';

/// A Riverpod controller that manages timer functionality with state management
@riverpod
class TimerController extends _$TimerController {
  Timer? _timer;
  static const _secondsPerHour = 3600;
  static const _secondsPerMinute = 60;

  @override
  FutureOr<TimerState> build() {
    // Clean up timer when the controller is disposed
    ref.onDispose(() {
      stopTimer();
    });
    return TimerState();
  }

  /// Extracts the seconds component (0-59) from total seconds
  int getSecond() {
    final value = state.valueOrNull;
    if (value == null) return 0;
    return value.totalSecond % _secondsPerMinute;
  }

  /// Extracts the minutes component (0-59) from total seconds
  int getMinute() {
    final value = state.valueOrNull;
    if (value == null) return 0;
    return (value.totalSecond % _secondsPerHour) ~/ _secondsPerMinute;
  }

  /// Extracts the hours component from total seconds
  int getHour() {
    final value = state.valueOrNull;
    if (value == null) return 0;
    return value.totalSecond ~/ _secondsPerHour;
  }

  /// Starts the timer if it's not already running
  void startTimer() {
    try {
      stopTimer(); // Ensure any existing timer is cancelled

      final value = state.valueOrNull;
      if (value == null || value.totalSecond <= 0) return;

      if (value.timerState != TimerConcreteState.start) {
        setTimerState(TimerConcreteState.start);
      }
      _timer = Timer.periodic(
        const Duration(seconds: 1),
        (_) => _updateTimer(),
      );
    } catch (error, stackTrace) {
      // Handle timer creation errors
      state = AsyncError('Failed to start timer: $error', stackTrace);
    }
  }

  /// Updates the timer state and checks for completion
  void _updateTimer() {
    try {
      final value = state.valueOrNull;
      if (value == null) {
        stopTimer();
        return;
      }

      if (value.timerState == TimerConcreteState.start &&
          value.totalSecond > 0) {
        final newSeconds = value.totalSecond - 1;
        state = AsyncData(value.copyWith(totalSecond: newSeconds));

        if (newSeconds <= 0) {
          _handleTimerCompletion();
        }
      } else if (value.timerState != TimerConcreteState.start) {
        stopTimer();
      }
    } catch (error, stackTrace) {
      // Handle timer update errors and stop the timer to prevent further issues
      stopTimer();
      state = AsyncError('Timer update failed: $error', stackTrace);
    }
  }

  /// Handles timer completion by updating state and cleaning up
  void _handleTimerCompletion() {
    try {
      stopTimer();

      final value = state.valueOrNull;
      if (value != null) {
        state = AsyncData(
          value.copyWith(timerState: TimerConcreteState.reset, totalSecond: 0),
        );
      }
    } catch (error, stackTrace) {
      // Handle completion errors
      stopTimer(); // Ensure timer is stopped even if state update fails
      state = AsyncError('Timer completion failed: $error', stackTrace);
    }
  }

  /// Stops the timer and cancels the internal Timer
  void stopTimer() {
    _timer?.cancel();
    _timer = null;
  }

  /// Sets the total seconds based on hours, minutes, and seconds input
  /// All parameters must be non-negative
  void setTotalSecond({int hours = 0, int minutes = 0, int seconds = 0}) {
    try {
      if (hours < 0 || minutes < 0 || seconds < 0) return;
      if (minutes >= 60 || seconds >= 60) return;

      final totalSeconds =
          (hours * _secondsPerHour) + (minutes * _secondsPerMinute) + seconds;

      // Check for potential overflow
      if (totalSeconds < 0) {
        state = AsyncError('Timer value overflow detected', StackTrace.current);
        return;
      }

      final value = state.valueOrNull;
      if (value != null) {
        state = AsyncData(
          value.copyWith(
            totalSecond: totalSeconds,
            timerState: TimerConcreteState.reset,
          ),
        );
      }
      print('setTotalSecond: ${state.value!.totalSecond}');
    } catch (error, stackTrace) {
      state = AsyncError('Failed to set timer value: $error', stackTrace);
    }
  }

  /// Updates the timer's state
  void setTimerState(TimerConcreteState timerState) {
    try {
      final value = state.valueOrNull;
      if (value != null) {
        state = AsyncData(value.copyWith(timerState: timerState));
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to set timer state: $error', stackTrace);
    }
  }
}
