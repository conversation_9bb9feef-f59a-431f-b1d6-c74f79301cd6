// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'crashlytics_service_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$crashlyticsServiceHash() =>
    r'64b4678ee2b60677d6112a79fba7392829c030c0';

/// See also [crashlyticsService].
@ProviderFor(crashlyticsService)
final crashlyticsServiceProvider =
    AutoDisposeProvider<CrashlyticsRepository>.internal(
      crashlyticsService,
      name: r'crashlyticsServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$crashlyticsServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CrashlyticsServiceRef = AutoDisposeProviderRef<CrashlyticsRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
