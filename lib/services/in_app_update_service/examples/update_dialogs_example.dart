import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/in_app_update_service/presentation/controllers/in_app_update_controller.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/update_banner.dart';
import 'package:selfeng/services/in_app_update_service/presentation/widgets/install_update_prompt.dart';

/// Example screen demonstrating the simplified update system
/// Uses ONLY native Google Play dialogs + custom installation UI
class UpdateDialogsExampleScreen extends ConsumerWidget {
  const UpdateDialogsExampleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final updateState = ref.watch(inAppUpdateControllerProvider);
    final updateController = ref.read(inAppUpdateControllerProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Simplified Update System'),
        backgroundColor: const Color(0xFF1976D2),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Current state card
            _buildStateCard(updateState),
            const SizedBox(height: 24),

            // Installation UI (Only Custom UI)
            const Text(
              'Installation UI (Only Custom UI)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'These widgets only show when update is downloaded and ready to install:',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 12),
            const UpdateBanner(),
            const SizedBox(height: 8),
            const InstallUpdatePrompt(),
            const SizedBox(height: 24),

            // Native flow info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'Native Google Play Flow',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Update detection: Native Google Play dialog\n'
                    '• Download progress: Native Google Play notification\n'
                    '• Installation trigger: Custom UI (above widgets)',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Controller actions
            const Text(
              'Controller Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Initialize
            ElevatedButton.icon(
              onPressed: updateState.isLoading
                  ? null
                  : () => updateController.initialize(),
              icon: const Icon(Icons.refresh),
              label: const Text('Initialize Update Service'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 12),

            // Check for updates
            ElevatedButton.icon(
              onPressed: updateState.isLoading
                  ? null
                  : () => updateController.checkForUpdates(),
              icon: const Icon(Icons.search),
              label: const Text('Check for Updates'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 12),

            // Start flexible update
            if (updateState.isUpdateAvailable &&
                !updateState.isUpdateDownloaded)
              ElevatedButton.icon(
                onPressed: updateState.isLoading
                    ? null
                    : () => updateController.startFlexibleUpdate(),
                icon: const Icon(Icons.download),
                label: const Text('Start Flexible Update'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),

            // Complete flexible update
            if (updateState.isUpdateDownloaded)
              ElevatedButton.icon(
                onPressed: updateState.isLoading
                    ? null
                    : () => updateController.completeFlexibleUpdate(),
                icon: const Icon(Icons.install_desktop),
                label: const Text('Complete Flexible Update'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),

            const SizedBox(height: 12),

            // Clear error
            if (updateState.error != null)
              ElevatedButton.icon(
                onPressed: () => updateController.clearError(),
                icon: const Icon(Icons.clear),
                label: const Text('Clear Error'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStateCard(InAppUpdateState updateState) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Current Update State',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildStateRow('Loading', updateState.isLoading),
            _buildStateRow('Update Available', updateState.isUpdateAvailable),
            _buildStateRow('Update Downloaded', updateState.isUpdateDownloaded),
            _buildStateRow('High Priority', updateState.isHighPriority),
            if (updateState.error != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.red.withAlpha(77)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.red, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Error: ${updateState.error}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStateRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.grey,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: value ? Colors.black87 : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
