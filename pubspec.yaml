name: selfeng
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+50

environment:
  sdk: "^3.8.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  # Routing & Navigation
  go_router: ^16.0.0  # Used for in-app navigation

  # Networking
  dio: ^5.2.1+1  # Used for making HTTP requests to APIs

  # State Management
  equatable: ^2.0.5  # Utility for value object comparisons
  flutter_riverpod: ^2.0.2  # Provider for managing application state
  hooks_riverpod: ^2.4.4  # Hooks for working with Riverpod
  riverpod_annotation: ^2.2.1  # Code generation for Riverpod

  # Data Modeling
  freezed_annotation: ^3.0.0  # Code generation for creating immutable data classes
  json_annotation: ^4.9.0  # Code generation for JSON serialization/deserialization

  # Persistence
  shared_preferences: ^2.0.17  # Used for storing simple data on the device
  flutter_dotenv: ^6.0.0 # Load configuration at runtime from a .env file
  path_provider: ^2.1.4 # Provides access to commonly used locations on the filesystem

  # Internationalization
  intl: any  # Provides features for app localization

  # UI
  cupertino_icons: ^1.0.2  # Cupertino (iOS-style) icons
  google_fonts: ^6.2.1  # Integrates Google Fonts into your app
  flutter_launcher_icons: ^0.14.1  # Manages app launcher icons for different platforms
  flutter_animate: ^4.2.0+1  # Provides animation capabilities for UI elements
  flutter_svg: ^2.0.10+1 # Draw SVG files using Flutter
  scaled_size: ^0.5.0 #
  flutter_localizations:
    sdk: flutter  # Enables localization support for various languages

  # Firebase
  firebase_core: ^4.0.0  # Core library for using Firebase services
  firebase_crashlytics: ^5.0.0 # Automatically reports errors and crashes in your app
  firebase_auth: ^6.0.0  # Enables user authentication with Firebase
  firebase_analytics: ^12.0.0  # Tracks app usage data with Firebase Analytics
  firebase_messaging: ^16.0.0  # Enables push notifications with Firebase Cloud Messaging
  firebase_storage: ^13.0.0 # Files are stored in a Cloud Storage bucket
  cloud_functions: ^6.0.0 # A Flutter plugin to use the Cloud Functions for Firebase API
  google_sign_in: ^7.1.0  # Integrates Google Sign-In functionality

  firebase_performance_platform_interface: ^0.1.1+29  # Improves app performance monitoring with Firebase Performance Monitoring
  cloud_firestore: ^6.0.0 # This plugin enables you to interact with Cloud Firestore, a NoSQL database service from Firebase

  # Notifications
  flutter_local_notifications: ^19.0.0  # Enables displaying local notifications within the app
  flutter_timezone: ^4.0.0  # Provides functionalities for handling timezones
  device_info_plus: ^12.1.0  # Provides information about the user's device
  permission_handler: ^12.0.0+1 # Provides modal ask the user for permission while the app is running

  #Utilities
  flutter_sound: ^9.8.1 # a Flutter package allowing you to play and record audio for
  audio_session: ^0.2.0 # This plugin informs the operating system of the nature of your audio app
  record: ^6.0.0 # Audio recorder from microphone to a given file path or stream.
  audioplayers: ^6.0.0 # A Flutter plugin to play multiple simultaneously audio files, works for Android, iOS, Linux, macOS, Windows, and web.
  video_player: ^2.9.2 # A Flutter plugin for iOS, Android and Web for playing back video on a Widget surface.
  flick_video_player: ^0.9.0 # Flick Player wraps video_player under the hood and provides base architecture for developers to create their own set of UI and functionalities.
  wakelock_plus: ^1.3.2 # Prevent screen sleep
  in_app_update: ^4.2.3 # Enables in-app updates for Android and iOS
  share_plus: ^12.0.0 # Enables sharing content to other apps
  external_path: ^2.2.0 # Provides access to external storage directories like Downloads

  # Core
  flutter:
    sdk: flutter  # The base framework for your Flutter application
  change_case: ^2.1.0
  collection: ^1.18.0
  package_info_plus: ^9.0.0
  cached_network_image: ^3.4.1
  lottie: ^3.3.1
  firebase_app_check: ^0.4.0
  screenshot: ^3.0.0
  path: ^1.9.1

dev_dependencies:
  # Static Code Analysis (Linters)
  flutter_lints: ^6.0.0  # Analyzes code for potential issues
  custom_lint: ^0.7.0  # Likely a custom linter for your project's specific rules
  riverpod_lint: ^2.3.2  # Checks for best practices in Riverpod usage

  # Code Generation (Builders)
  build_runner: ^2.4.5  # Manages build tasks for code generation
  go_router_builder: ^3.0.0  # Generates code for navigation using GoRouter
  freezed: ^3.1.0  # Generates code for creating immutable data classes
  json_serializable: ^6.5.3  # Generates code for serialization and deserialization of JSON data

  # Testing
  flutter_test:
    sdk: flutter  # Provides the testing framework for Flutter apps
  http_mock_adapter: ^0.6.1  # Mocks HTTP responses for unit testing
  mocktail: ^1.0.3  # Mocking library for unit testing
  fake_cloud_firestore: any  # In-memory Firestore for unit tests (compatible with sealed types)
  test_coverage_badge: ^0.3.2  # Generates badges based on test coverage
  riverpod_generator: ^2.3.5  # Generates code for testing Riverpod providers

# Apply Icon Apps
flutter_launcher_icons:
  image_path: "assets/images/icons/icon.png"
  android: true
  ios: true
  remove_alpha_ios: true
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/icons/icon.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # Enable generation of localized Strings from arb files.
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env.dev
    - .env.stg
    - .env.prod
    - assets/images/icons/
    - assets/images/language/
    - assets/images/dashboard/
    - assets/images/main_lesson/
    - assets/images/main_lesson/dialog/
    - assets/images/main_lesson/pronunciation_challenge/
    - assets/images/main_lesson/speaking_arena/
    - assets/images/main_lesson/listening_mastery/
    - assets/images/main_lesson/conversation_video/
    - assets/images/questionnaire/
    - assets/images/diagnostic_test/
    - assets/images/onboarding/
    - assets/images/setting/
    - assets/images/certificate/
    - assets/images/games/
    - assets/images/games/memory_flash/
    - assets/images/games/word_scramble/
    - assets/animations/
    - assets/sounds/
    - assets/sounds/memory_flash/
    - assets/images/toc/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
